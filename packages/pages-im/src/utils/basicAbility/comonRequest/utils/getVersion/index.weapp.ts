// import {ONLINE_HOST} from '../../../../../constants/common';

export const getVitaVersion = () => {
    // const isOnlineProduction = ONLINE_HOST.includes(process.env.TARO_APP_API_HOST || '');
    const v = '2.3.343.release';
    // const v = isOnlineProduction
    //     ? // eslint-disable-next-line no-undef
    //     swan?.getEnvInfoSync()?.appVersion || 'unknow'
    //     : '*******.beta';
    return `wx_${v}`;
};