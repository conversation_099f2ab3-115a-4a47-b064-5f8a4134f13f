/* eslint-disable no-undef */
function parseCookie(cookieString: string): Record<string, string> {
    return cookieString
        .split('; ')
        .map(cookie => cookie.split('='))
        .reduce(
            (acc, [key, ...value]) => {
                acc[key] = decodeURIComponent(value.join('='));

                return acc;
            },
            {} as Record<string, string>
        );
}

export const getBaiduId = (): Promise<string> => {
    return new Promise((resolve, reject) => {
        try {
            const localeBaiduId = wx?.getStorageSync('BAIDUID');

            if (localeBaiduId) {
                resolve(localeBaiduId);

                return;
            }
            wx?.request({
                url: `https://www.baidu.com/favicon.ico?from=ubcloggergetbaiduid&_=${new Date().getTime()}`,
                success: res => {
                    if (res.header['Set-Cookie']) {
                        const cookies = parseCookie(res.header['Set-Cookie']);
                        cookies?.BAIDUID && wx.setStorageSync('BAIDUID', cookies.BAIDUID);
                        resolve(cookies?.BAIDUID);
                    }
                },
                fail: err => {
                    reject(err);
                }
            });
        } catch (err) {
            reject(err);
        }
    });
};
