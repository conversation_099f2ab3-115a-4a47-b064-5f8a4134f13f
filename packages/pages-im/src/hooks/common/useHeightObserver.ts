import {useCallback, useEffect, useRef} from 'react';
import {createSelectorQuery, nextTick} from '@tarojs/taro';
import {useScrollControl} from '../common/useScrollControl';

/**
 * 监听元素高度变化并自动滚动到底部的hook
 * @param elementId 需要监听的元素id
 * @param needScrollToBottom 是否需要开启监听滚动到底部
 * @param symbol 滚动的标识，用于定位问题，默认为'dom_height_observer'
 * @returns 检查高度变化的函数
 */
export const useHeightObserver = (
    elementId: string,
    needScrollToBottom?: boolean,
    symbol?: string,
    pageControl?: string
) => {
    const lastHeightRef = useRef<number>(0);
    const {scrollToBottom} = useScrollControl(pageControl || 'triageStreamScrollControl');

    // 通用的高度变化检查函数
    const checkHeightChange = useCallback(() => {
        if (!elementId) return;

        // 小程序环境使用selector查询
        if (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'swan') {
            createSelectorQuery()
                ?.select(`#${elementId}`)
                ?.boundingClientRect()
                ?.exec((dom: Array<{height: number}>) => {
                    if (dom?.length && dom[0]?.height) {
                        const currentHeight = dom[0].height;
                        if (currentHeight !== lastHeightRef.current && lastHeightRef.current > 0) {
                            nextTick(() => {
                                scrollToBottom?.(symbol || 'dom_height_observer');
                            });
                        }
                        lastHeightRef.current = currentHeight;
                    }
                });
        } else {
            // H5环境直接获取元素高度
            const element = document.getElementById(elementId);
            if (element) {
                const currentHeight = element.getBoundingClientRect().height;
                if (currentHeight !== lastHeightRef.current && lastHeightRef.current > 0) {
                    nextTick(() => {
                        scrollToBottom?.(symbol || 'dom_height_observer');
                    });
                }
                lastHeightRef.current = currentHeight;
            }
        }
    }, [elementId, scrollToBottom, symbol]);

    // H5环境使用ResizeObserver监听高度变化
    useEffect(() => {
        if (process.env.TARO_ENV === 'h5' && needScrollToBottom) {
            let resizeObserver: ResizeObserver | null = null;
            const element = document.getElementById(elementId);

            if (element) {
                resizeObserver = new ResizeObserver(entries => {
                    entries.forEach(entry => {
                        const {height} = entry.contentRect;
                        if (height !== lastHeightRef.current && lastHeightRef.current > 0) {
                            nextTick(() => {
                                scrollToBottom?.(symbol || 'dom_height_observer');
                            });
                        }
                        lastHeightRef.current = height;
                    });
                });

                resizeObserver.observe(element);
            }

            return () => {
                if (resizeObserver) {
                    resizeObserver.disconnect();
                }
            };
        }
    }, [elementId, scrollToBottom, needScrollToBottom, symbol]);

    // 小程序环境使用定时器轮询检查高度变化
    useEffect(() => {
        if (
            (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'swan') &&
            needScrollToBottom
        ) {
            const interval = setInterval(() => {
                checkHeightChange();
            }, 200);

            return () => {
                clearInterval(interval);
            };
        }
    }, [checkHeightChange, needScrollToBottom]);

    return checkHeightChange;
};
