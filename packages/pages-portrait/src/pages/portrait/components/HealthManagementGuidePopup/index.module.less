.container {
    position: relative;
    padding: 40px 32px 32px;
    background: #fff;
    border-radius: 24px 24px 0 0;
    max-height: 75vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .closeButton {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(0 0 0 / 5%);
        border-radius: 50%;
        cursor: pointer;
        z-index: 10;
        transition: background-color 0.2s ease;

        &:hover {
            background: rgb(0 0 0 / 10%);
        }

        .closeIcon {
            font-size: 40px;
            color: #666;
            line-height: 1;
        }
    }

    .fixedTop {
        flex-shrink: 0;
        padding-top: 20px;

        .description {
            color: #272933;
            margin-bottom: 16px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 32px;
            line-height: 1.5;
            text-align: left;
        }

        .subText {
            font-size: 28px;
            color: #f60;
            margin-bottom: 40px;
            font-family: PingFang SC;
        }
    }

    .scrollableArea {
        flex: 1;
        min-height: 0;
        margin-bottom: 30px;

        .illustrationContainer {
            height: 100%;
            width: 100%;

            .illustrationImage {
                width: 100%;
                display: block;
                border-radius: 16px;
                background: #f8f9fa;
                min-height: 200px;
            }

            .placeholderImage {
                width: 100%;
                height: 300px;
                background: #f8f9fa;
                border-radius: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 2px dashed #e5e5e5;
                margin-bottom: 16px;
                position: relative;

                .placeholderText {
                    font-size: 32px;
                    color: #848691;
                    font-family: PingFang SC;
                    font-weight: 400;
                }

                &::before {
                    content: '📋';
                    font-size: 60px;
                    display: block;
                    margin-bottom: 16px;
                    opacity: 0.6;
                }
            }
        }
    }

    .fixedBottom {
        flex-shrink: 0;

        .privacyNote {
            text-align: center;
            margin: 30px 0 32px;

            .privacyContent {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-wrap: wrap;
                gap: 8px;
            }

            .privacyIcon {
                font-size: 24px;
            }

            .privacyText {
                font-size: 24px;
                color: #999;
                font-family: PingFang SC;
            }

            .privacyLink {
                font-size: 24px;
                color: #00cfa3;
                font-family: PingFang SC;
                transition: color 0.2s ease;

                &:hover {
                    color: #00b894;
                }

                &:active {
                    color: #00a085;
                }
            }
        }

        .buttonGroup {
            padding-top: 16px;
            border-top: 1px solid #f0f0f0;

            .confirmButton {
                width: 100%;
                height: 88px;
                background: linear-gradient(134deg, #00cfa3 0%, #00d3ea 100%);
                color: #fff;
                border: none;
                font-size: 32px;
                font-weight: 500;
                font-family: PingFang SC;
                transition: all 0.3s ease;
                box-shadow: 0 4px 12px rgb(0 207 163 / 30%);

                &::after {
                    border: none;
                }

                /* 注释掉 在微信小程序不支持 伪类多个同时写
                &:disabled,
                &.loading {
                    background: #ccc;
                    box-shadow: none;
                    cursor: not-allowed;
                    transform: none;

                    &.loading {
                        position: relative;

                        &::before {
                            content: '';
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            width: 20px;
                            height: 20px;
                            margin: -10px 0 0 -60px;
                            border: 2px solid #fff;
                            border-radius: 50%;
                            border-top-color: transparent;
                        }
                    }
                }

                &:hover:not(:disabled, .loading) {
                    transform: translateY(-2px);
                    box-shadow: 0 6px 16px rgb(0 207 163 / 40%);
                }

                &:active:not(:disabled, .loading) {
                    transform: translateY(0);
                    box-shadow: 0 2px 8px rgb(0 207 163 / 30%);
                }
                */
            }
        }
    }
}
