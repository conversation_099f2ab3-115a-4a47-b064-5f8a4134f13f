/**
 * @file EndorseCard 背书组件
 * @author: <EMAIL>
 */
import {pxTransform} from '@tarojs/taro';
import {type FC, memo, useState, useCallback, useEffect} from 'react';
import cx from 'classnames';
import {View, Text} from '@tarojs/components';
import {WImage, Popup} from '@baidu/wz-taro-tools-core';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';
import {isEmpty} from '../../../../pages-im/src/utils';
import SanjiaTag from '../../../ImDoctorCard/components/SanjiaTag';
import Portal from '../../../../ui-cards-common/Portal';
import {imgUrlMap} from '../../../../pages-im/src/constants/resourcesOnBos';
import {
    ubcCommonViewSend,
    ubcCommonClkSend
} from '../../../../pages-im/src/utils/generalFunction/ubc';

import {EndorseCardProps} from './index.d';
import styles from './index.module.less';

const EndorseCard: FC<EndorseCardProps> = props => {
    const {content: info} = props;
    const {content = []} = info?.headerInfo?.actionInfo?.interactionInfo?.popupInfo || {};
    const avatarList = info?.headerInfo?.avatarList || [];
    const reviewNote = info?.headerInfo?.reviewNote || '';

    // 背书popup
    const [isEndorsePopupShow, setIsEndorsePopupShow] = useState(false);

    // 头像宽度，用于计算移动距离
    const width = 66;

    // 跳转到医生主页
    const handleJumpDoctorHome = useCallback((url: string) => {
        // 后端返回全路径，手百特殊处理
        if (process.env.TARO_ENV === 'swan') {
            const isFullPath = url.startsWith('https://') || url.startsWith('http://');

            if (isFullPath) {
                const urlObj = new URL(url);
                const targetUrl = urlObj.pathname + urlObj.search;
                return navigate({
                    url: targetUrl,
                    openType: 'navigate'
                });
            }
        }
        navigate({
            url,
            openType: 'navigate'
        });
    }, []);

    useEffect(() => {
        ubcCommonViewSend({
            value: 'beishu'
        });
    }, []);

    return (
        <View className={cx(styles.pageContainer, 'wz-plr-45 wz-pt-18')}>
            <View
                className={cx(
                    styles.endorseHeader,
                    'wz-flex wz-col-center wz-row-between wz-plr-36'
                )}
                onClick={() => {
                    setIsEndorsePopupShow(true);
                    ubcCommonClkSend({value: 'beishu'});
                }}
            >
                <View className={cx(styles.content, 'wz-flex')}>
                    <View
                        className={styles.doctorAcatorGroup}
                        style={{
                            width:
                                (avatarList?.length >= 3 && pxTransform(140)) ||
                                (avatarList?.length === 2 && pxTransform(100)) ||
                                pxTransform(66)
                        }}
                    >
                        <View className={styles.rotation}>
                            <View className={cx(styles.rotationContainer, 'wz-col-center')}>
                                {!isEmpty(avatarList) &&
                                    avatarList.map((item, index) => {
                                        return (
                                            <View
                                                key={index}
                                                className={styles.expertAvatar}
                                                style={{
                                                    marginLeft: pxTransform(+`${-width * (1 / 3)}`)
                                                }}
                                            >
                                                <WImage
                                                    key={index}
                                                    round
                                                    className={styles.expertAvatarIcon}
                                                    src={item}
                                                />
                                            </View>
                                        );
                                    })}
                            </View>
                        </View>
                    </View>
                    <View
                        className={cx(
                            styles.endorse‌HeaderDesc,
                            'wz-fw-500 wz-fs-42 wz-taro-ellipsis'
                        )}
                    >
                        {reviewNote ? reviewNote.replace(/<[^>]*>/g, '') : ''}
                    </View>
                </View>
                <WImage
                    src={imgUrlMap.endorseLineRightArrowIcon}
                    className={cx(styles.arrow, 'wz-ml-27')}
                />
            </View>
            {/* 背书popup */}
            <Portal>
                <Popup
                    open={isEndorsePopupShow}
                    onClose={() => setIsEndorsePopupShow(false)}
                    rounded
                    catchMove={false}
                    style={{height: '80%'}}
                    placement='bottom'
                    className={styles.popup}
                >
                    <Popup.Close />
                    <View className={cx(styles.popupContentWrap, 'wz-flex wz-col-top')}>
                        <View className={styles.headerWrap}>
                            <WImage
                                src={imgUrlMap.endorsePopupTitleIcon}
                                className={styles.bigtitle}
                            />
                            <View className={cx(styles.tipTitle, 'wz-fs-42 wz-fw-500 wz-mt-9')}>
                                内容由以下专家与百度健康医学中心共同完成审阅
                            </View>
                        </View>
                        <View className={styles.container}>
                            {!isEmpty(content) &&
                                content.map(el => (
                                    <View
                                        key={el.docId}
                                        className={cx(styles.docItemWrap, 'wz-plr-45 wz-pt-54')}
                                        onClick={() => {
                                            el?.homeUrlParams?.url &&
                                                handleJumpDoctorHome(el.homeUrlParams.url);
                                        }}
                                    >
                                        <View
                                            className={cx(
                                                styles.docItem,
                                                'wz-flex wz-col-top wz-pb-45 wz-taro-hairline--bottom'
                                            )}
                                        >
                                            <WImage
                                                src={el.portrait}
                                                className={styles.avatar}
                                                round
                                            />
                                            <View
                                                className={cx(
                                                    styles.content,
                                                    'wz-flex wz-col-top wz-ml-30'
                                                )}
                                            >
                                                <View>
                                                    <Text
                                                        className={cx(
                                                            styles.name,
                                                            'wz-fs-48 wz-fw-500 wz-mr-18'
                                                        )}
                                                    >
                                                        {el.name}
                                                    </Text>
                                                    <Text
                                                        className={cx(
                                                            styles.level,
                                                            'wz-fs-42 wz-mr-18'
                                                        )}
                                                    >
                                                        {el.title}
                                                    </Text>
                                                    <Text
                                                        className={cx(
                                                            styles.department,
                                                            'wz-fs-42'
                                                        )}
                                                    >
                                                        {el.department}
                                                    </Text>
                                                </View>
                                                <View className='wz-flex wz-mt-12'>
                                                    <Text
                                                        className={cx(
                                                            styles.hospitalName,
                                                            'wz-fs-42 wz-mr-24'
                                                        )}
                                                    >
                                                        {el.hospital}
                                                    </Text>
                                                    {el.hospitalLevel && (
                                                        <SanjiaTag
                                                            content={el.hospitalLevel}
                                                            variant='contained'
                                                            bgColor='#EBF7EF'
                                                            color='#39B362'
                                                        />
                                                    )}
                                                </View>
                                                {+el.likeCount ? (
                                                    <View className='wz-mt-24 wz-fs-42'>
                                                        <Text
                                                            className={cx(
                                                                styles.count,
                                                                'wz-fw-500'
                                                            )}
                                                        >
                                                            {el.likeCount}位
                                                        </Text>
                                                        <Text>{el.department}患者好评推荐</Text>
                                                    </View>
                                                ) : null}
                                                {el.hospitalFudanRank || el.departmentFudanRank ? (
                                                    <View
                                                        className={cx(
                                                            styles.fudanWrap,
                                                            'wz-flex wz-br-24 wz-mt-30 wz-pr-21'
                                                        )}
                                                    >
                                                        <WImage
                                                            src={imgUrlMap.fudanIcon}
                                                            className={styles.fudanIcon}
                                                        />
                                                        {el.hospitalFudanRank && (
                                                            <Text className='wz-fs-36'>
                                                                {el.hospitalFudanRank}
                                                            </Text>
                                                        )}

                                                        {el.departmentFudanRank && (
                                                            <Text
                                                                className={cx(
                                                                    el.hospitalFudanRank &&
                                                                        styles.fudanGap,
                                                                    el.hospitalFudanRank &&
                                                                        'wz-ml-21',
                                                                    'wz-fs-36'
                                                                )}
                                                            >
                                                                {el.departmentFudanRank}
                                                            </Text>
                                                        )}
                                                    </View>
                                                ) : null}
                                                {el.goodAt ? (
                                                    <View
                                                        className={cx(
                                                            styles.goodAt,
                                                            'wz-mt-24 wz-taro-ellipsis'
                                                        )}
                                                    >
                                                        擅长：
                                                        <Text className='wz-fs-42'>
                                                            {el.goodAt}
                                                        </Text>
                                                    </View>
                                                ) : null}
                                            </View>
                                        </View>
                                    </View>
                                ))}
                        </View>
                    </View>
                </Popup>
            </Portal>
        </View>
    );
};

export default memo(EndorseCard);
