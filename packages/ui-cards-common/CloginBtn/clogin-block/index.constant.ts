import {AgreementProps} from './index.d';

export const agreement = {
    text: '请阅读并勾选页面底部协议',
    info: [
        {
            type: 'text',
            text: '查看'
        },
        {
            type: 'link',
            text: '《服务协议》',
            // eslint-disable-next-line max-len
            url: '/wenzhen/pages/agreement/index?pd=med&openapi=1&from_sf=1&resource_id=5216&vn=med&title=《服务协议》&type=all&atn=agreement'
        },
        {
            type: 'link',
            text: '《隐私协议》',
            // eslint-disable-next-line max-len
            url: '/wenzhen/pages/agreement/index?pd=med&openapi=1&from_sf=1&resource_id=5216&vn=med&title=《隐私政策》&type=allsecret&atn=agreement'
        },
        {
            type: 'link',
            text: '《免责声明》',
            // eslint-disable-next-line max-len
            url: '/wenzhen/pages/agreement/index?pd=med&openapi=1&from_sf=1&resource_id=5216&vn=med&title=《免责声明》&type=allagree&atn=agreement'
        }
    ]
} as AgreementProps;

export const wxAgreement = {
    text: '请阅读并勾选页面底部协议',
    info: [
        {
            type: 'text',
            text: '查看'
        },
        {
            type: 'link',
            text: '《百度用户协议》',
            url: `/wenzhen/pages/common/webpage/index?url=${encodeURIComponent(
                'https://wappass.baidu.com/passport/agreement?adapter=3&lang=zh-cn'
            )}&unNeedLogin=1`
        },
        {
            type: 'link',
            text: '《百度隐私政策》',
            url: `/wenzhen/pages/common/webpage/index?url=${encodeURIComponent(
                'https://wappass.baidu.com/passport/agreement?adapter=3&lang=zh-cn&personal=1'
            )}&unNeedLogin=1`
        },
        {
            type: 'link',
            text: '《百度健康用户协议》',
            url: '/wenzhen/pages/common/agreementList/index'
        }
    ]
} as AgreementProps;
