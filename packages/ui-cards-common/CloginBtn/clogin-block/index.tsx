import {View, Text} from '@tarojs/components';
import React, {FC, memo, useCallback, useState} from 'react';
import {WiseCheckSelectedSolid, WiseSelected} from '@baidu/wz-taro-tools-icons';
import {getSystemInfo} from '../../../pages-im/src/utils/taro';
import {showToast} from '../../../pages-im/src/utils/customShowToast';

import {initPlugin} from '../../../pages-im/src/utils/generalFunction/loginApi';
import {navigate} from '../../../pages-im/src/utils/basicAbility/commonNavigate';
import {reportWeAnalysisEvent} from '../reportWeAnalysisEvent/index';

import LoginButton from '../c-login-button/index.weapp';
import type {LoginBlockProps} from './index.d';
import {agreement, wxAgreement} from './index.constant';

import './index.less';

const systemInfo = getSystemInfo();

const realAgreement = process.env.TARO_ENV === 'weapp' ? wxAgreement : agreement;

const LoginBlock: FC<LoginBlockProps> = (props: LoginBlockProps) => {
    const {isLogin, data, successCallback} = props;

    const {screenHeight = 0} = systemInfo;
    const [agreementChecked, setAgreementChecked] = useState<boolean>(false);
    const env = process.env.TARO_ENV;
    const loginButtonText = env === 'swan' ? '百度授权登录' : env === 'weapp' ? '一键登录' : '';

    const onTapStart = () => {
        if (!agreementChecked) {
            showToast({title: '请阅读并勾选页面底部协议', icon: 'none'});
        }
    };

    const onLoginSuccess = () => {
        if (process.env.TARO_ENV === 'weapp') {
            successCallback?.();
        } else {
            // 处理登录成功跳转方式
            const url = data?.to;
            navigate({
                url,
                openType: 'redirect'
            });
        }
    };

    const onLoginFail = () => {
        // 处理登录失败
    };

    const changeAgreement = () => {
        setAgreementChecked(!agreementChecked);
    };

    const onTapAgreement = item => {
        navigate({
            url: item.url,
            openType: 'navigate'
        });
    };

    const toOtherLoginType = useCallback(() => {
        reportWeAnalysisEvent({
            event: 'clk_other_login',
            properties: {
                interaction_Of_login: 'page'
            }
        });

        initPlugin({
            backUrl: data?.to || ''
        });

        navigate({
            url: 'plugin://pass-plugin/bind?page=login&loginType=sms',
            openType: 'navigate'
        });
    }, [data?.to]);

    return (
        <View className='mall-login-block'>
            {/* 中间图标与登录按钮 */}
            <View className='mall-login-block__content' style={{bottom: `${0.5 * screenHeight}Px`}}>
                <View className='mall-login-block__content__logo' />
                {!agreementChecked ? (
                    <View className='mall-login-block__content__loginButton' onClick={onTapStart}>
                        {loginButtonText}
                    </View>
                ) : (
                    <LoginButton
                        isLogin={isLogin}
                        callbackUrl={data && data.to ? data.to : ''}
                        className='mall-login-block__content__loginButton'
                        onLoginFail={onLoginFail}
                        onLoginSuccess={onLoginSuccess}
                        wxLoginInteractionType={
                            process.env.TARO_ENV === 'weapp' ? 'page' : undefined
                        }
                    >
                        {loginButtonText}
                    </LoginButton>
                )}
                {process.env.TARO_ENV === 'weapp' && (
                    <View
                        className='mall-login-block__content__otherLoginButton'
                        onClick={toOtherLoginType}
                    >
                        其他登录方式
                    </View>
                )}
            </View>

            {/* 底部协议 */}
            <View className='mall-login-block__footer'>
                <View className='mall-login-block__footer__check' onClick={changeAgreement}>
                    {agreementChecked && <WiseCheckSelectedSolid color='#00C8C8 ' size={66} />}
                    {!agreementChecked && <WiseSelected color='#B8B8B8 ' size={66} />}
                    <Text className='mall-login-block__footer__check__text c-gap-left-small'>
                        {agreement.text}
                    </Text>
                </View>
                <View>
                    {realAgreement.info.map((item, index) => {
                        return (
                            <View
                                className='mall-login-block__footer__agreement c-font-xnormal'
                                key={index}
                                onClick={() => {
                                    onTapAgreement(item);
                                }}
                            >
                                {item.type === 'text' ? (
                                    <Text className='mall-login-block__footer__text'>
                                        {item.text}
                                    </Text>
                                ) : null}
                                {item.type === 'link' ? (
                                    <Text className='mall-login-block__footer__link'>
                                        {item.text}
                                    </Text>
                                ) : null}
                            </View>
                        );
                    })}
                </View>
            </View>
        </View>
    );
};

LoginBlock.defaultProps = {
    isLogin: false
};

export default memo(LoginBlock);
