import type {ReportWeAnalysisEventArgs} from './eventPropertiesMap';

/**
 *
 * @description 上报 we 分析事件
 * @param arg
 */
export const reportWeAnalysisEvent = (args: ReportWeAnalysisEventArgs) => {
    console.info('该方法仅在微信小程序生效', args);
};

export const triageSessionValMap = {
    zhusu: 'submit_question',
    patient: 'select_patient_of_question',
    patient_deny: 'create_patient_of_question',
    gender: 'choose_gender_of_question',
    age: 'select_age_of_question',
    selectAge: 'select_age_of_question'
};

export const getTriageSessionData = (): object => {
    console.info('该方法仅在微信小程序生效');

    return {};
};
