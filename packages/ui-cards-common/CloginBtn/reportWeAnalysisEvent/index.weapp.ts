/* eslint-disable no-undef */
import {getStorageSync} from '@tarojs/taro';

import {API_HOST} from '../../../pages-im/src/models/apis/host';

import {VITA_TRIAGE_SESSION} from '../../../pages-im/src/constants/storageEnv';
import {getWechatPassUserInfo} from '../../../pages-im/src/utils/generalFunction/login';

import {eventPropertiesMap, type ReportWeAnalysisEventArgs} from './eventPropertiesMap';

/**
 *
 * @description 上报 we 分析事件VITA_TRIAGE_SESSION
 * @param arg {ReportWeAnalysisEventArgs}
 */
export const reportWeAnalysisEvent = (args: ReportWeAnalysisEventArgs) => {
    try {
        const {event, properties} = args;
        if (!eventPropertiesMap[event]) {
            console.error(`reportWeAnalysisEvent 出错：${event} 在 eventPropertiesMap 中不存在`);

            return;
        }

        if (
            !haveSameKeys(eventPropertiesMap[event], properties) &&
            event !== 'choose_service_of_question'
        ) {
            console.error(
                `reportWeAnalysisEvent 出错：${event}的属性与 eventPropertiesMap 中不一致`
            );

            return;
        }

        const bduss = getWechatPassUserInfo()?.bduss;
        const loginData = getStorageSync('wx_login_type_data');

        const reportData = {
            ...properties,
            login_status: bduss ? 1 : 0,
            is_online:
                (API_HOST?.includes('jiankang.baidu.com') ||
                    API_HOST?.includes('expert.baidu.com')) &&
                process.env.NODE_ENV === 'production'
                    ? 1
                    : 0,
            ...(bduss && loginData?.type
                ? {
                    cur_login_type: loginData?.type
                }
                : bduss
                    ? {cur_login_type: 0}
                    : {})
        };

        console.info('reportWeAnalysisEvent 数据上报：', reportData);

        wx.reportEvent?.(event, reportData);
    } catch (err) {
        console.error('reportWeAnalysisEvent 出错：', err);
    }
};

/**
 *
 * @description 判断两个对象是否具有相同的键名
 * @param obj1 第一个对象
 * @param obj2 第二个对象
 * @returns 如果两个对象具有相同的键名，返回true；否则返回false
 */
function haveSameKeys<T extends object>(obj1: T, obj2: T): boolean {
    return JSON.stringify(Object.keys(obj1).sort()) === JSON.stringify(Object.keys(obj2).sort());
}

export const triageSessionValMap = {
    zhusu: 'submit_question',
    patient: 'select_patient_of_question',
    patient_deny: 'create_patient_of_question',
    gender: 'choose_gender_of_question',
    age: 'select_age_of_question',
    selectAge: 'select_age_of_question'
};

export const getTriageSessionData = () => {
    const userSession = JSON.parse(getStorageSync(VITA_TRIAGE_SESSION));

    let userSessionVal = {};
    Object.values(triageSessionValMap).forEach(i => {
        userSessionVal = {
            ...userSessionVal,
            ...(userSession[i] || {})
        };
    });

    return userSessionVal;
};
