/**
 *
 * @description we 分析事件属性 map，具体参考 we 分析事件属性字典
 */
export const eventPropertiesMap = {
    submit_question: {
        directed_inquiry: 0,
        operator_of_event: 1,
        submit_question_type: 0
    },
    choose_gender_of_question: {
        directed_inquiry: 0,
        operator_of_event: 1,
        submit_question_type: 0,
        patient_source: 0,
        patient_gender: 1
    },
    select_patient_of_question: {
        directed_inquiry: 0,
        operator_of_event: 1,
        submit_question_type: 0,
        patient_source: 0
    },
    create_patient_of_question: {
        directed_inquiry: 0,
        operator_of_event: 1,
        submit_question_type: 0,
        patient_source: 0
    },
    select_age_of_question: {
        directed_inquiry: 0,
        operator_of_event: 1,
        submit_question_type: 0,
        patient_gender: 1,
        patient_age: 0,
        patient_source: 0
    },
    choose_service_of_question: {
        directed_inquiry: 0,
        operator_of_event: 1,
        submit_question_type: 0,
        patient_source: 0
    },
    complete_payment_of_question: {
        directed_inquiry: 0
    },
    to_login_page: {
        source_page: '',
        landing_page: ''
    },
    login_successful: {
        source_page: '',
        landing_page: ''
    },
    view_triage_page: {
        directed_inquiry: 0
    },
    // 用户搜索内容
    seach_by_user: {
        search_query: ''
    },
    // 登录弹层展示
    view_login_popup: {},
    clk_quick_login: {
        interaction_of_login: 'page'
    },
    clk_quick_login_shadow: {
        interaction_of_login: 'page',
        login_error_tips: '',
        login_error_query: '',
        is_error: 0
    },
    login_trigger_loginapi: {
        interaction_of_login: 'page',
        login_error_tips: '',
        login_error_query: '',
        is_error: 0
    },
    login_bind_account: {
        interaction_of_login: 'page',
        login_error_tips: '',
        login_error_query: '',
        is_error: 0
    },
    login_init_plugin: {
        interaction_of_login: 'page',
        login_error_tips: '',
        login_error_query: '',
        is_error: 0
    },
    login_getuserinfo: {
        interaction_of_login: 'page',
        login_error_tips: '',
        login_error_query: '',
        is_error: 0
    },
    login_authorize_mobile: {
        interaction_of_login: 'page',
        login_error_tips: '',
        login_error_query: '',
        is_error: 0
    },
    clk_other_login: {
        interaction_of_login: 'page'
    },
    login_error: {
        interaction_of_login: 'page',
        login_error_tips: '',
        login_error_query: ''
    },
    clk_homebanner: {
        homebanner_name: '',
        homebanner_url: '',
        homebanner_ext: ''
    },
    // 待2.4.8稳定后去掉
    clk_home_goldlocation: {
        home_goldlocation_name: '',
        home_goldlocation_url: '',
        home_goldlocation_ext: ''
    },
    // 待2.4.8稳定后去掉
    clk_home_quicknavigation: {
        home_quicknavigation_name: '',
        home_quicknavigation_url: '',
        home_quicknavigation_ext: ''
    },
    // 首页2.4.8版本金刚位
    clk_home_goldlocation_v2: {
        home_goldlocation_name: '',
        home_goldlocation_url: '',
        home_goldlocation_ext: ''
    },
    // 首页2.4.8版本ai服务模块
    clk_home_askai: {
        home_askai_name: '',
        home_askai_url: '',
        home_askai_ext: ''
    },
    // 患者报道-审核结果页浏览
    view_patientinvitation_detail: {
        // 0未报到，1报到中，2报道成功，3报道失败
        patientinvitation_approval_status: 0
    },
    // 患者报道-审核结果页医生卡点击
    clk_patientinvitation_detail_toim: {
        // 0未报到，1报到中，2报道成功，3报道失败
        patientinvitation_approval_status: 0
    },
    // 患者报道-审核结果页点击去交流
    clk_patientinvitation_detail_doctorcard: {
        // 0未报到，1报到中，2报道成功，3报道失败
        patientinvitation_approval_status: 0
    },
    view_page: {
        from_of_params: '',
        ref_of_params: '',
        params_of_page: ''
    },
    clk_share_button: {
        title: '',
        share_path: '',
        from_of_params: '',
        ref_of_params: '',
        params_of_page: ''
    }
};

type EventPropertiesMapType = typeof eventPropertiesMap;
type EventProperties = EventPropertiesMapType[keyof EventPropertiesMapType];

export interface ReportWeAnalysisEventArgs {
    event: keyof EventPropertiesMapType;
    properties: EventProperties;
}
