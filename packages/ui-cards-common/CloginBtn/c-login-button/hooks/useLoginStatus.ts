import {useState, useEffect, useCallback} from 'react';
import {eventCenter} from '@tarojs/taro';
import globalDataStore from '../utils/globalData/index';

const LOGIN_STATUS_KEY = 'loginStatus';
const LOGIN_STATUS_CHANGE_EVENT = 'loginStatusChange';

interface LoginStatusData {
    isLogin: boolean;
    userInfo?: unknown;
}

/**
 * 登录状态管理 Hook
 * 提供简单的登录状态管理和多组件状态同步
 */
export const useLoginStatus = (initialStatus = false) => {
    // 从全局存储获取初始状态
    const getStoredStatus = useCallback((): LoginStatusData => {
        const stored = globalDataStore.get(LOGIN_STATUS_KEY);
        return stored || {isLogin: initialStatus};
    }, [initialStatus]);

    const [loginStatus, setLoginStatus] = useState<LoginStatusData>(getStoredStatus);

    // 更新登录状态
    const updateLoginStatus = useCallback((isLogin: boolean, userInfo?: unknown) => {
        const newStatus: LoginStatusData = {isLogin, userInfo};

        // 更新本地状态
        setLoginStatus(newStatus);

        // 保存到全局存储
        globalDataStore.set(LOGIN_STATUS_KEY, newStatus);

        // 通知其他组件
        eventCenter.trigger(LOGIN_STATUS_CHANGE_EVENT, newStatus);

        console.info('登录状态已更新:', newStatus);
    }, []);

    // 监听其他组件的状态变化
    useEffect(() => {
        const handleStatusChange = (newStatus: LoginStatusData) => {
            setLoginStatus(newStatus);
        };

        eventCenter.on(LOGIN_STATUS_CHANGE_EVENT, handleStatusChange);

        return () => {
            eventCenter.off(LOGIN_STATUS_CHANGE_EVENT, handleStatusChange);
        };
    }, []);

    // 初始化时同步状态
    useEffect(() => {
        const storedStatus = getStoredStatus();
        if (storedStatus.isLogin !== loginStatus.isLogin) {
            setLoginStatus(storedStatus);
        }
    }, [getStoredStatus, loginStatus.isLogin]);

    // 便捷方法
    const login = useCallback(
        (userInfo?: unknown) => {
            updateLoginStatus(true, userInfo);
        },
        [updateLoginStatus]
    );

    const logout = useCallback(() => {
        updateLoginStatus(false);
    }, [updateLoginStatus]);

    return {
        isLogin: loginStatus.isLogin,
        userInfo: loginStatus.userInfo,
        login,
        logout,
        updateLoginStatus
    };
};
