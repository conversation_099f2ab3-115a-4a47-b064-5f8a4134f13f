import Taro from '@tarojs/taro';
import {getAppInfo} from '@baidu/vita-utils-shared';
import {showToast} from '../../pages-im/src/utils/customShowToast';
import {getSystemInfo} from '../../pages-im/src/utils/taro';

import {getCurrentPage} from '../taroUtils';
import {extend} from '../../pages-im/src/utils';
import httpRequest from '../../pages-im/src/utils/basicAbility/comonRequest/common';

import {navigate, OpenType} from '../../pages-im/src/utils/basicAbility/commonNavigate';
import {getQuery} from './common';

let payState = 'init';

export const invokeWechatPay = async userConf => {
    if (payState !== 'init') {
        return;
    }
    payState = 'paying';
    // 5s手动重置
    setTimeout(() => {
        payState = 'init';
    }, 5000);

    const conf = extend(
        {
            success: null,
            fail: null,
            complete: null
        },
        userConf
    );
    Taro.requestPayment({
        ...conf.wxPayInfo,
        success() {
            showToast({
                title: '支付成功',
                icon: 'success'
            });
            // 支付成功页面跳转(优先级从高到低)：
            // 1、conf.success
            // 2、conf.success.url;
            // 3、下单接口payResultUrl+returnData
            // 4、后退
            if (typeof conf.success === 'function') {
                // taro 相关 case：https://github.com/NervJS/taro/issues/12421
                // Tips: 延迟跳转避免下游页面无法获取到参数
                Taro.hideToast();
                setTimeout(() => {
                    conf.success();
                }, 700);
            }
            // else if (conf.data.srt) {
            //     debugger;
            //     const success = JSON.parse(conf.data.srt);
            //     navigate({
            //         url: success.url,
            //         openType: success.openType
            //     });
            // }
            else {
                httpRequest({
                    // todo renyi03 url
                    // 获取支付成功回调接口
                    // PAY_SUCCESS_RETURN: '/tradeflow/b2c/order/paySuccessReturn',
                    // url: Apis.PAY_SUCCESS_RETURN,
                    url: '',
                    data: {
                        ...conf.data,
                        extraInfo: conf.data.returnData,
                        platformType: getAppInfo().platformType,
                        payMode: 1
                    }
                })
                    .then(res => {
                        const [err, resp] = res;
                        // @ts-expect-error 后续修复
                        if (!err && resp && resp.data && resp.data.payResultUrl) {
                            // 处理登录成功跳转方式
                            let openType = '';
                            if (getCurrentPage().isOnlyPage) {
                                openType = 'relaunch';
                            } else {
                                openType = 'redirect';
                            }

                            navigate({
                                openType: openType as OpenType,
                                // @ts-expect-error 后续修复
                                url: resp?.data?.payResultUrl
                            });
                        }
                    })
                    .catch(e => {
                        console.error('e', e);
                    });
            }
        },
        fail(err) {
            // @links https://developers.weixin.qq.com/miniprogram/dev/api/payment/wx.requestPayment.html
            const sys = getSystemInfo();
            let errCode = -1;
            Taro.reportAnalytics('payment_err_record', {
                sys: (sys.system || '').toLowerCase().replace(/[\d.]/g, ''),
                code: err,
                version: sys.version,
                error: err && err.errMsg,
                host: sys.host,
                orderid: JSON.stringify(conf),
                t: Date.now()
            });

            let errorMessage = '支付失败';
            if (err.errMsg && err.errMsg.indexOf('cancel') !== -1) {
                errorMessage = '支付取消';
                errCode = 2;
            }
            showToast({
                title: errorMessage,
                icon: 'none'
            });

            if (typeof conf.fail === 'function') {
                conf.fail({
                    errCode
                });
            } else if (conf.data && conf.data.ftr) {
                const fail = JSON.parse(conf.data.ftr);
                navigate({
                    url: fail.url,
                    openType: fail.openType
                });
            }
            throw err;
        },
        complete() {
            payState = 'init';
            if (typeof conf.complete === 'function') {
                conf.complete();
            }
        }
    });
};

/**
 * 调起支付
 *
 * @param {Object} userConf 用户配置
 * @param {string} userConf.url 糯米支付地址
 * @param {Function} userConf.success 支付成功回调
 * @param {Function} userConf.fail 支付失败回调
 * @param {Object} userConf.wxPayInfo 微信支付配置
 * @param {Function} userConf.complete 支付结束回调
 */
export const pay = userConf => {
    const q = getQuery(userConf.url) || {};

    invokeWechatPay({
        data: q,
        wxPayInfo: userConf.wxPayInfo,
        urlType: userConf.urlType,
        success: userConf.success,
        fail: userConf.fail,
        complete: userConf.complete
    });
};

export const hisPay = userConfig => {
    pay(userConfig);
};
