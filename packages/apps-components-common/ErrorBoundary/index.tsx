import React, {Component, PropsWithChildren} from 'react';
import {weirwoodErrorReport} from '../../utils-shared/weirwood'; // 封装的错误上报方法

export function createErrorBoundary<P>(Page: React.ComponentType<P>) {
    return class ErrorBoundary extends Component<PropsWithChildren<P>> {
        static getDerivedStateFromError() {
            return {
                hasError: true
            };
        }

        state = {
            hasError: null
        };

        constructor(props: PropsWithChildren<P>) {
            super(props);
        }

        componentDidCatch(error) {
            // 在 非 H5 环境使用对应方法 todo: 完成小程序环境判断
            // (inH5 为各自项目中实现，判断当前是否为 H5 环境；由于 Taro 会同构多端，如果为原生则不需要判断)
            weirwoodErrorReport(error);
        }

        render() {
            if (this.state.hasError) {
                console.error('has error');
            }

            // 可根据error做展示错误页面，此处为防止不重要错误阻塞正常页面，暂时不用
            return <Page {...this.props} />;
        }
    };
}
