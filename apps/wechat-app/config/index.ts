import {defineConfig, type UserConfigExport} from '@tarojs/cli';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import {BASE_NAME} from '../path/index';
import devConfig from './dev';
import prodConfig from './prod';

// 公共的 webpack 配置
const getCommonChainConfig = chain => {
    chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin);

    // 只处理 workspace 包
    chain.module
        .rule('script')
        .test(/\.(js|jsx|ts|tsx)$/)
        .include.add(/packages\/.*/) // 只处理 packages 目录下的文件
        .add(/node_modules\/marked/) // 处理 marked npm包
        .end()
        .use('babel-loader')
        .loader('babel-loader')
        .options({
            presets: [
                [
                    'babel-preset-taro',
                    {
                        framework: 'react',
                        ts: true,
                        compiler: 'webpack5'
                    }
                ]
            ]
        });

    // 设置 alias 官方bug,临时解决dangerouslySetInnerHTML，小程序端无法渲染问题
    // https://github.com/NervJS/taro/issues/16892
    chain.resolve.alias.set('@tarojs/runtime', require.resolve('@tarojs/runtime'));
};

// mini 平台特有的配置
const getMiniChainConfig = (chain, webpack) => {
    // 先应用公共配置
    getCommonChainConfig(chain);
    chain.merge({
        output: {
            chunkLoadingGlobal: `webpackJsonpIn${BASE_NAME}`
        }
    });

    /**
     * 修复lodash-es 微信下报错 Cannot read property 'now' of undefined
     * https://github.com/NervJS/taro/issues/8098#issuecomment-**********
     */
    chain.plugin('mini_define').use(webpack.DefinePlugin, [
        {
            global: 'globalThis'
        }
    ]);
};

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig<'webpack5'>(async merge => {
    const baseConfig: UserConfigExport<'webpack5'> = {
        projectName: 'wechat-mini-app',
        date: '2025-8-29',
        designWidth: 1242,
        deviceRatio: {
            640: 2.34 / 2,
            750: 1,
            828: 1.81 / 2,
            1242: 2 / 3.312
        },
        sourceRoot: 'src',
        outputRoot: 'dist',
        plugins: [],
        defineConstants: {},
        copy: {
            patterns: [],
            options: {}
        },
        framework: 'react',
        compiler: {
            type: 'webpack5',
            prebundle: {
                enable: false,
                timings: true,
                exclude: ['@baidu/weirwood-mp-sdk', '@searchfe/user-agent', 'sse-kit']
            }
        },
        cache: {
            enable: false // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
        },
        mini: {
            postcss: {
                pxtransform: {
                    enable: true,
                    config: {unitPrecision: 8}
                },
                cssModules: {
                    enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
                    config: {
                        namingPattern: 'module', // 转换模式，取值为 global/module
                        generateScopedName: 'vita_[hash:base64:5]'
                    }
                }
            },
            webpackChain: getMiniChainConfig
        },
        h5: {
            publicPath: '/',
            staticDirectory: 'static',
            output: {
                filename: 'js/[name].[hash:8].js',
                chunkFilename: 'js/[name].[chunkhash:8].js'
            },
            miniCssExtractPluginOption: {
                ignoreOrder: true,
                filename: 'css/[name].[hash].css',
                chunkFilename: 'css/[name].[chunkhash].css'
            },
            postcss: {
                autoprefixer: {
                    enable: true,
                    config: {}
                },
                cssModules: {
                    enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
                    config: {
                        namingPattern: 'module', // 转换模式，取值为 global/module
                        generateScopedName: '[name]__[local]___[hash:base64:5]'
                    }
                }
            },
            webpackChain(chain) {
                chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin);
            }
        },
        rn: {
            appName: 'taroDemo',
            postcss: {
                cssModules: {
                    enable: false // 默认为 false，如需使用 css modules 功能，则设为 true
                }
            }
        }
    };

    process.env.BROWSERSLIST_ENV = process.env.NODE_ENV;

    if (process.env.NODE_ENV === 'development') {
        // 本地开发构建配置（不混淆压缩）
        return merge({}, baseConfig, devConfig);
    }
    // 生产构建配置（默认开启压缩混淆等）
    return merge({}, baseConfig, prodConfig);
});
