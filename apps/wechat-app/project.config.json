{"miniprogramRoot": "dist/", "projectname": "wechat-app", "description": "vita 微信小程序", "appid": "wx8911e67a01f3eaea", "setting": {"urlCheck": false, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": false, "minified": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {}}