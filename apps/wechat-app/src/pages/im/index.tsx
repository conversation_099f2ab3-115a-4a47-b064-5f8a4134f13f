import {useState, useEffect, useCallback, useRef} from 'react';
import {Provider} from 'jotai';
import {View} from '@tarojs/components';
import {eventCenter} from '@tarojs/taro';
import {vibrateShort} from '@baidu/vita-utils-shared';
import cx from 'classnames';
import {Im, utils} from '@baidu/vita-pages-im';
import {Service, Profile} from '@baidu/vita-pages-common';
import {Portrait} from '@baidu/vita-pages-portrait';
import {useGetUrlParams} from '../../../../../packages/pages-im/src/hooks/common';
import {Portal} from '../../../../../packages/ui-cards-common';
import {triageStreamAtomStore} from '../../../../../packages/pages-im/src/store/triageStreamAtom';
import CustomTabBar from '../../../../../packages/apps-components-common/CustomTabBar';
import {TabId} from '../../../../../packages/apps-components-common/CustomTabBar/index.d';
import styles from './index.module.less';

// eslint-disable-next-line no-undef
const TAB_COMPONENTS: Record<TabId, () => JSX.Element> = {
    [TabId.AI]: () => (
        <Provider store={triageStreamAtomStore}>
            <Portal.provider>
                <Im />
                <Portal.slot />
            </Portal.provider>
        </Provider>
    ),
    [TabId.PORTRAIT]: () => (
        <Portal.provider>
            <Portrait />
            <Portal.slot />
        </Portal.provider>
    ),
    [TabId.SERVICE]: () => <Service />,
    [TabId.PROFILE]: () => (
        <Portal.provider>
            <Profile />
            <Portal.slot />
        </Portal.provider>
    )
};

const isIpx = utils.validateIsIphoneX();

export default function Index() {
    const {tab = TabId.AI, sf_ref = ''} = useGetUrlParams();
    const [activeTab, setActiveTab] = useState<TabId | null>(null);
    const [showBar] = useState(true);
    const [inited, setInited] = useState(false);
    const visitedTabs = useRef<Set<TabId>>(new Set());
    // eslint-disable-next-line no-undef
    const tabComponentMap = useRef<Map<TabId, JSX.Element>>(new Map());
    useEffect(() => {
        activeTab && visitedTabs.current.add(activeTab);
    }, [activeTab]);

    // 异步初始化 tab 和 showBar 状态
    useEffect(() => {
        async function checkSfRef() {
            const tabIds = [TabId.AI, TabId.PORTRAIT, TabId.SERVICE, TabId.PROFILE];
            let nextTab: TabId = TabId.AI;

            if (tabIds.includes(tab)) {
                nextTab = tab as TabId;
            }

            setActiveTab(nextTab);
            // 确保首次访问时立即加入 visitedTabs
            visitedTabs.current.add(nextTab);
            setInited(true);
        }

        checkSfRef();
    }, [sf_ref, tab]);

    const renderContent = () => {
        if (!inited || !activeTab) return null;

        // eslint-disable-next-line no-undef
        const nodes: JSX.Element[] = [];

        visitedTabs.current.forEach(tabId => {
            if (!tabComponentMap.current.has(tabId)) {
                const Comp = TAB_COMPONENTS[tabId]?.();
                if (Comp) {
                    tabComponentMap.current.set(tabId, Comp);
                }
            }

            const Comp = tabComponentMap.current.get(tabId);
            if (Comp) {
                nodes.push(
                    <View
                        key={tabId}
                        className={cx(styles.tabPanel, {
                            [styles.visible]: tabId === activeTab,
                            [styles.hidden]: tabId !== activeTab
                        })}
                    >
                        {Comp}
                    </View>
                );
            }
        });

        return <>{nodes}</>;
    };

    const onTabPress = useCallback(tab => {
        // 若在语音播报 则关闭
        eventCenter.trigger('stopTTS');
        vibrateShort();
        const nextTab = tab?.id;
        setActiveTab(nextTab);
        visitedTabs.current.add(nextTab);

        if (process.env.TARO_ENV === 'h5') {
            const url = new URL(window.location.href);
            url.searchParams.set('tab', nextTab);
            window.history.replaceState({}, '', url.toString());
        }
    }, []);

    if (!inited) return null;

    const pageContent = (
        <View
            className={cx(styles.tabPage, activeTab === 'ai' ? styles.tabPageIm : '')}
            style={process.env.TARO_ENV !== 'h5' ? {height: '100vh'} : {}}
        >
            <View className={styles.tabContent}>{renderContent()}</View>
            {showBar ? (
                <CustomTabBar activeTab={activeTab || TabId.AI} onTabPress={onTabPress} />
            ) : (
                <View style={{background: 'transparent', height: '10px'}}></View>
            )}
            {isIpx && (
                <View
                    style={{
                        background: activeTab === 'ai' ? 'transparent' : '#fff',
                        height: '14px'
                    }}
                />
            )}
        </View>
    );

    return pageContent;
}
